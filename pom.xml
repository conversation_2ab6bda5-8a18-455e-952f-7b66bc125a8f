<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
		 xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>
	<parent>
		<groupId>org.springframework.boot</groupId>
		<artifactId>spring-boot-starter-parent</artifactId>
		<version>2.7.14</version>
		<relativePath/> <!-- lookup parent from repository -->
	</parent>
	<groupId>com.dwdo.hotdesk</groupId>
	<artifactId>staggered-payment-service</artifactId>
	<version>0.0.1-SNAPSHOT</version>
	<name>staggered-payment-service</name>
	<description>Staggered Payment</description>
	<properties>
		<java.version>17</java.version>
		<spring-cloud.version>2021.0.9</spring-cloud.version>
		<maven-surefire-plugin.version>3.0.0-M3</maven-surefire-plugin.version>
	</properties>

	<distributionManagement>
		<repository>
			<id>central</id>
			<name>Artifactory CIMB NIAGA-releases</name>
			<url>https://artifactory.cimbniaga.co.id:443/artifactory/arjuna-backend/</url>
		</repository>
		<snapshotRepository>
			<id>snapshots</id>
			<name>Artifactory CIMB NIAGA-snapshots</name>
			<url>https://artifactory.cimbniaga.co.id:443/artifactory/arjuna-backend/</url>
		</snapshotRepository>
	</distributionManagement>

	<repositories>
		<repository>
			<id>spring-repo-milestone</id>
			<name>Spring Repository - Milestones</name>
			<url>https://repo.spring.io/milestone</url>
		</repository>
		<repository>
			<id>central</id>
			<url>https://repo1.maven.org/maven2</url>
		</repository>
		<repository>
			<id>gradle-workaround</id>
			<url>https://repo.spring.io/milestone</url>
			<snapshots>
				<enabled>false</enabled>
			</snapshots>
		</repository>
	</repositories>

	<dependencyManagement>
		<dependencies>
			<dependency>
				<groupId>org.springframework.cloud</groupId>
				<artifactId>spring-cloud-gcp-dependencies</artifactId>
				<version>1.2.3.RELEASE</version>
				<type>pom</type>
				<scope>import</scope>
			</dependency>
			<dependency>
				<groupId>com.google.cloud</groupId>
				<artifactId>libraries-bom</artifactId>
				<version>16.4.0</version>
				<type>pom</type>
				<scope>import</scope>
			</dependency>
			<dependency>
				<groupId>org.springframework.cloud</groupId>
				<artifactId>spring-cloud-dependencies</artifactId>
				<version>${spring-cloud.version}</version>
				<type>pom</type>
				<scope>import</scope>
			</dependency>
		</dependencies>
	</dependencyManagement>

	<dependencies>
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter</artifactId>
		</dependency>

		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-test</artifactId>
			<scope>test</scope>
		</dependency>

		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-data-jpa</artifactId>
		</dependency>

		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-validation</artifactId>
			<version>2.7.14</version>
		</dependency>

		<dependency>
			<groupId>ch.vorburger.mariaDB4j</groupId>
			<artifactId>mariaDB4j</artifactId>
			<!-- 			<version>3.0.1</version> -->
			<version>2.5.3</version>
			<scope>test</scope>
		</dependency>

		<dependency>
			<groupId>org.mariadb.jdbc</groupId>
			<artifactId>mariadb-java-client</artifactId>
			<version>2.7.2</version> <!-- Use the latest version -->
		</dependency>

		<dependency>
			<groupId>commons-dbutils</groupId>
			<artifactId>commons-dbutils</artifactId>
			<version>1.7</version>
		</dependency>

		<!-- https://mvnrepository.com/artifact/mysql/mysql-connector-java -->
		<dependency>
			<groupId>mysql</groupId>
			<artifactId>mysql-connector-java</artifactId>
			<version>8.0.33</version>
		</dependency>

		<dependency>
			<groupId>org.projectlombok</groupId>
			<artifactId>lombok</artifactId>
			<version>1.18.30</version>
			<optional>true</optional>
		</dependency>

		<dependency>
			<groupId>junit</groupId>
			<artifactId>junit</artifactId>
			<scope>test</scope>
		</dependency>

		<dependency>
			<groupId>org.junit.jupiter</groupId>
			<artifactId>junit-jupiter</artifactId>
			<version>5.8.2</version>
			<scope>test</scope>
		</dependency>

		<dependency>
			<groupId>org.junit.platform</groupId>
			<artifactId>junit-platform-suite</artifactId>
			<version>1.8.2</version>
			<scope>test</scope>
		</dependency>

		<dependency>
			<groupId>org.mockito</groupId>
			<artifactId>mockito-core</artifactId>
			<scope>test</scope>
		</dependency>

		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-web</artifactId>
			<version>2.7.9</version>
		</dependency>

		<!-- https://mvnrepository.com/artifact/org.modelmapper/modelmapper -->
		<dependency>
			<groupId>org.modelmapper</groupId>
			<artifactId>modelmapper</artifactId>
			<version>3.2.0</version>
		</dependency>

		<dependency>
			<groupId>commons-io</groupId>
			<artifactId>commons-io</artifactId>
			<version>2.15.1</version>
		</dependency>

		<dependency>
			<groupId>org.springframework.security</groupId>
			<artifactId>spring-security-crypto</artifactId>
			<version>5.1.5.RELEASE</version>
		</dependency>
		<dependency>
			<groupId>io.minio</groupId>
			<artifactId>minio</artifactId>
			<version>8.5.8</version>
		</dependency>

		<dependency>
			<groupId>com.github.ulisesbocchio</groupId>
			<artifactId>jasypt-spring-boot-starter</artifactId>
			<version>2.0.0</version>
		</dependency>
		<dependency>
			<groupId>com.h2database</groupId>
			<artifactId>h2</artifactId>
		</dependency>

		<dependency>
			<groupId>org.springframework.cloud</groupId>
			<artifactId>spring-cloud-starter-openfeign</artifactId>
		</dependency>

		<dependency>
			<groupId>org.springframework.cloud</groupId>
			<artifactId>spring-cloud-starter-sleuth</artifactId>
		</dependency>

		<dependency>
			<groupId>io.jsonwebtoken</groupId>
			<artifactId>jjwt</artifactId>
			<version>0.9.0</version>
		</dependency>

		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-security</artifactId>
		</dependency>

		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-json</artifactId>
		</dependency>

		<dependency>
			<groupId>io.springfox</groupId>
			<artifactId>springfox-swagger2</artifactId>
			<version>3.0.0</version>
		</dependency>

		<dependency>
			<groupId>io.springfox</groupId>
			<artifactId>springfox-bean-validators</artifactId>
			<version>3.0.0</version>
		</dependency>
		<dependency>
			<groupId>org.apache.commons</groupId>
			<artifactId>commons-lang3</artifactId>
		</dependency>

		<dependency>
			<groupId>com.fasterxml.jackson.datatype</groupId>
			<artifactId>jackson-datatype-hibernate5</artifactId>
		</dependency>
		<dependency>
			<groupId>com.fasterxml.jackson.datatype</groupId>
			<artifactId>jackson-datatype-jsr310</artifactId>
		</dependency>
		<dependency>
			<groupId>com.fasterxml.jackson.core</groupId>
			<artifactId>jackson-databind</artifactId>
		</dependency>
		<dependency>
			<groupId>org.apache.tika</groupId>
			<artifactId>tika-parsers</artifactId>
			<version>1.17</version>
		</dependency>

		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-amqp</artifactId>
		</dependency>

		<dependency>
			<groupId>org.apache.commons</groupId>
			<artifactId>commons-csv</artifactId>
			<version>1.9.0</version>
		</dependency>

		<!-- Add Apache POI for Excel processing -->
		<dependency>
			<groupId>org.apache.poi</groupId>
			<artifactId>poi</artifactId>
			<version>5.2.3</version>
		</dependency>
		<dependency>
			<groupId>org.apache.poi</groupId>
			<artifactId>poi-ooxml</artifactId>
			<version>5.2.3</version>
		</dependency>
		<dependency>
			<groupId>org.springframework.boot</groupId>
			<artifactId>spring-boot-starter-amqp</artifactId>
		</dependency>

	</dependencies>

	<profiles>
		<profile>
            <id>sonar</id>
            <activation>
                <activeByDefault>true</activeByDefault>
            </activation>
            <properties>
                <sonar.host.url>https://sast.cimbniaga.co.id</sonar.host.url>
                <sonar.login>squ_28cf5370a35abb68256db2b2168fa8f39a4e04b6</sonar.login>
                <sonar.token>squ_28cf5370a35abb68256db2b2168fa8f39a4e04b6</sonar.token>
                <sonar.coverage.exclusions>
                    **/config/*.java, **/config/**/*.java, **/config/**/**/*.java, **/config/**/**/**/*.java,
                    **/domain/*.java, **/domain/**/*.java, **/domain/**/**/*.java, **/domain/**/**/**/*.java,
                    **/constant/*.java, **/constant/**/*.java, **/constant/**/**/*.java, **/constant/**/**/**/*.java,
                    **/exception/*.java, **/exception/**/*.java,
                    **/cimbniaga/octomobile/Application.java
                </sonar.coverage.exclusions>
                <sonar.java.coveragePlugin>jacoco</sonar.java.coveragePlugin>
                <sonar.dynamicAnalysis>reuseReports</sonar.dynamicAnalysis>
                <sonar.language>java</sonar.language>
                <sonar.java.libraries>${user.home}/.m2/repository</sonar.java.libraries>
                <sonar.sources>src/main/java</sonar.sources>
                <sonar.java.source>11</sonar.java.source>
                <sonar.binaries>target/classes</sonar.binaries>
            </properties>
        </profile>
		<profile>
			<id>sit</id>
			<dependencies>
				<dependency>
					<groupId>org.springframework.boot</groupId>
					<artifactId>spring-boot-starter-undertow</artifactId>
				</dependency>
				<dependency>
					<groupId>org.springframework.cloud</groupId>
					<artifactId>spring-cloud-gcp-starter-logging</artifactId>
				</dependency>
				<dependency>
					<groupId>com.google.cloud</groupId>
					<artifactId>google-cloud-logging-logback</artifactId>
				</dependency>
				<dependency>
					<groupId>org.springframework.cloud</groupId>
					<artifactId>spring-cloud-gcp-starter-sql-mysql</artifactId>
				</dependency>
			</dependencies>
			<build>
				<plugins>
					<plugin>
						<artifactId>maven-clean-plugin</artifactId>
						<configuration>
							<filesets>
								<fileset>
									<directory>target/classes/static/</directory>
								</fileset>
							</filesets>
						</configuration>
					</plugin>
					<plugin>
						<groupId>org.springframework.boot</groupId>
						<artifactId>spring-boot-maven-plugin</artifactId>
						<configuration>
							<mainClass>${start-class}</mainClass>
							<executable>true</executable>
						</configuration>
						<executions>
							<execution>
								<goals>
									<goal>build-info</goal>
								</goals>
							</execution>
						</executions>
					</plugin>
					<plugin>
						<groupId>pl.project13.maven</groupId>
						<artifactId>git-commit-id-plugin</artifactId>
					</plugin>
				</plugins>
			</build>
			<properties>
				<!-- default Spring profiles -->
				<spring.profiles.active>sit</spring.profiles.active>
			</properties>
		</profile>
		<profile>
			<id>uat</id>
			<dependencies>
				<dependency>
					<groupId>org.springframework.boot</groupId>
					<artifactId>spring-boot-starter-undertow</artifactId>
				</dependency>
				<dependency>
					<groupId>org.springframework.cloud</groupId>
					<artifactId>spring-cloud-gcp-starter-logging</artifactId>
				</dependency>
				<dependency>
					<groupId>com.google.cloud</groupId>
					<artifactId>google-cloud-logging-logback</artifactId>
				</dependency>
				<dependency>
					<groupId>org.springframework.cloud</groupId>
					<artifactId>spring-cloud-gcp-starter-sql-mysql</artifactId>
				</dependency>
			</dependencies>
			<build>
				<plugins>
					<plugin>
						<artifactId>maven-clean-plugin</artifactId>
						<configuration>
							<filesets>
								<fileset>
									<directory>target/classes/static/</directory>
								</fileset>
							</filesets>
						</configuration>
					</plugin>
					<plugin>
						<groupId>org.springframework.boot</groupId>
						<artifactId>spring-boot-maven-plugin</artifactId>
						<configuration>
							<mainClass>${start-class}</mainClass>
							<executable>true</executable>
						</configuration>
						<executions>
							<execution>
								<goals>
									<goal>build-info</goal>
								</goals>
							</execution>
						</executions>
					</plugin>
					<plugin>
						<groupId>pl.project13.maven</groupId>
						<artifactId>git-commit-id-plugin</artifactId>
					</plugin>
				</plugins>
			</build>
			<properties>
				<!-- default Spring profiles -->
				<spring.profiles.active>uat</spring.profiles.active>
			</properties>
		</profile>
		<profile>
			<id>dev</id>
			<activation>
				<activeByDefault>true</activeByDefault>
			</activation>
			<dependencies>
				<dependency>
					<groupId>org.springframework.boot</groupId>
					<artifactId>spring-boot-starter-undertow</artifactId>
				</dependency>
				<dependency>
					<groupId>org.springframework.boot</groupId>
					<artifactId>spring-boot-devtools</artifactId>
					<optional>true</optional>
				</dependency>
				<dependency>
					<groupId>com.h2database</groupId>
					<artifactId>h2</artifactId>
				</dependency>
			</dependencies>
			<properties>
				<!-- default Spring profiles -->
				<spring.profiles.active>dev</spring.profiles.active>
			</properties>
		</profile>
		<profile>
			<id>prod</id>
			<dependencies>
				<dependency>
					<groupId>org.springframework.boot</groupId>
					<artifactId>spring-boot-starter-undertow</artifactId>
				</dependency>
				<dependency>
					<groupId>org.springframework.cloud</groupId>
					<artifactId>spring-cloud-gcp-starter-logging</artifactId>
				</dependency>
				<dependency>
					<groupId>com.google.cloud</groupId>
					<artifactId>google-cloud-logging-logback</artifactId>
				</dependency>
				<dependency>
					<groupId>org.springframework.cloud</groupId>
					<artifactId>spring-cloud-gcp-starter-sql-mysql</artifactId>
				</dependency>
			</dependencies>
			<build>
				<plugins>
					<plugin>
						<artifactId>maven-clean-plugin</artifactId>
						<configuration>
							<filesets>
								<fileset>
									<directory>target/classes/static/</directory>
								</fileset>
							</filesets>
						</configuration>
					</plugin>
					<plugin>
						<groupId>org.springframework.boot</groupId>
						<artifactId>spring-boot-maven-plugin</artifactId>
						<configuration>
							<mainClass>${start-class}</mainClass>
						</configuration>
						<executions>
							<execution>
								<goals>
									<goal>build-info</goal>
								</goals>
							</execution>
						</executions>
					</plugin>
					<plugin>
						<groupId>pl.project13.maven</groupId>
						<artifactId>git-commit-id-plugin</artifactId>
					</plugin>
				</plugins>
			</build>
			<properties>
				<!-- default Spring profiles -->
				<spring.profiles.active>prod</spring.profiles.active>
			</properties>
		</profile>
		<profile>
			<id>war</id>
			<build>
				<plugins>
					<plugin>
						<groupId>org.apache.maven.plugins</groupId>
						<artifactId>maven-war-plugin</artifactId>
						<version>${maven-war-plugin.version}</version>
						<executions>
							<execution>
								<goals>
									<goal>war</goal>
								</goals>
								<phase>package</phase>
							</execution>
						</executions>
						<configuration>
							<warSourceIncludes>WEB-INF/**,META-INF/**</warSourceIncludes>
							<failOnMissingWebXml>false</failOnMissingWebXml>
						</configuration>
					</plugin>
				</plugins>
			</build>
		</profile>
	</profiles>

	<build>
		<plugins>
			<plugin>
				<groupId>org.springframework.boot</groupId>
				<artifactId>spring-boot-maven-plugin</artifactId>
				<configuration>
					<image>
						<builder>paketobuildpacks/builder-jammy-base:latest</builder>
					</image>
				</configuration>
			</plugin>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-surefire-plugin</artifactId>
				<configuration>
					<skipTests>true</skipTests>
				</configuration>
			</plugin>
		</plugins>
	</build>

</project>
