package com.dwdo.hotdesk.service;

import com.dwdo.hotdesk.dto.request.SingleValidRequestDTO;
import com.dwdo.hotdesk.dto.response.GeneralBodyResponse;
import com.dwdo.hotdesk.dto.PaymentDataDTO;
import com.dwdo.hotdesk.dto.SlikSanctionDataDTO;
import com.dwdo.hotdesk.repository.PaymentSubmissionRepository;
import com.dwdo.hotdesk.restcontrolleradvice.CustomBadRequestException;
import com.dwdo.hotdesk.service.feign.EmployeeClient;
import com.dwdo.hotdesk.service.feign.ExecutionClient;
import com.dwdo.hotdesk.service.feign.request.ExecutionRequest;
import com.dwdo.hotdesk.service.feign.response.ApiResponse;
import com.dwdo.hotdesk.service.feign.response.EpiccEmployeeProfileImpl;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;
import java.io.IOException;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.util.*;


@Slf4j
@Service
@RequiredArgsConstructor
public class  PaymentValidationService {

    @Autowired
    private EmployeeClient employeeClient;

    @Autowired
    private ExecutionClient executionClient;

    @Autowired
    private PaymentSubmissionRepository submissionRepository;

    @Value("${logic.code.staggered-payment-validation}")
    private String staggeredPaymentValidationLogicCode;

    private static final String EXCEL_TYPE_XLS = "application/vnd.ms-excel";
    private static final String EXCEL_TYPE_XLSX = "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet";
    private static final String NIP_VALID_MESSAGE = "NIP is valid";
    private static final String NIP_INVALID_MESSAGE = "NIP is invalid";
    private static final String GRADE_VALID_MESSAGE = "Grade is valid";
    private static final String GRADE_INVALID_MESSAGE = "Grade is invalid";
    private static final String PAYMENT_TYPE_VALID_MESSAGE = "Payment type is valid";
    private static final String PAYMENT_TYPE_INVALID_MESSAGE = "Payment type is invalid";

    private static final Set<String> VALID_PAYMENT_TYPES = Set.of(
        "Bonus Staggered",
        "Retention Bonus",
        "Token",
        "Performance Staggered",
        "Salary Adjustment",
        "Promotion",
        "Retention Salary"
    );

    private static final Set<String> VALID_GRADES = Set.of(
        "U1", "U2", "U3", "U4", "U5", "U6", "U7", "U8", "U9", "U10", "U11"
    );

    public GeneralBodyResponse validateExcelFile(MultipartFile file) {
        if (!isExcelFile(file)) {
            throw new CustomBadRequestException(400, "Invalid File", "Only Excel files are allowed");
        }

        try {
            log.info("[ValidationService] Starting to process Excel file: {}", file.getOriginalFilename());
            List<PaymentDataDTO> paymentDataList = processExcelFileToDTO(file);
            log.info("[ValidationService] Processed {} payment records", paymentDataList.size());
            
            return GeneralBodyResponse.builder()
                .code(200)
                .status("OK")
                .message("File validated successfully")
                .data(paymentDataList)
                .build();
        } catch (IOException e) {
            log.error("[ValidationService] Error processing Excel file", e);
            throw new CustomBadRequestException(400, "Processing Error", "Failed to process the Excel file");
        }
    }

    private boolean isExcelFile(MultipartFile file) {
        String contentType = file.getContentType();
        return contentType != null && (
                contentType.equals(EXCEL_TYPE_XLS) ||
                contentType.equals(EXCEL_TYPE_XLSX)
        );
    }

    private List<PaymentDataDTO> processExcelFileToDTO(MultipartFile file) throws IOException {
        List<PaymentDataDTO> paymentDataList = new ArrayList<>();
        Map<Integer, String> validationErrors = new HashMap<>();
        
        try (Workbook workbook = new XSSFWorkbook(file.getInputStream())) {
            Sheet sheet = workbook.getSheetAt(0);
            
            Iterator<Row> rowIterator = sheet.iterator();
            if (!rowIterator.hasNext()) {
                return paymentDataList;
            }
            
            rowIterator.next();
            
            int rowNum = 1;
            while (rowIterator.hasNext()) {
                Row row = rowIterator.next();
                rowNum++;

                if (isEmptyRow(row)) {
                    log.debug("Skipping empty row {}", rowNum);
                    continue;
                }

                try {
                    PaymentDataDTO paymentData = extractPaymentDataFromRow(row);
                    paymentDataList.add(paymentData);
                } catch (CustomBadRequestException e) {
                    validationErrors.put(rowNum, e.getMessage());
                    log.warn("Skipping invalid row {}: {}", rowNum, e.getMessage());
                }
            }
        }

        bulkFetchSlikAndSanctionData(paymentDataList);

        checkAndMarkDuplicates(paymentDataList);

        if (paymentDataList.isEmpty() && !validationErrors.isEmpty()) {
            StringBuilder errorMsg = new StringBuilder("No valid payment records found. Validation errors:\n");
            validationErrors.forEach((row, error) ->
                errorMsg.append("Row ").append(row).append(": ").append(error).append("\n"));
            throw new CustomBadRequestException(400, "Validation Error", errorMsg.toString());
        }

        return paymentDataList;
    }

    private PaymentDataDTO extractPaymentDataFromRow(Row row) {
        String nip = getStringCellValue(row.getCell(0));
        String name = getStringCellValue(row.getCell(1));
        String grade = getStringCellValue(row.getCell(2));
        String paymentType = getStringCellValue(row.getCell(3));
        BigDecimal amount = getBigDecimalCellValue(row.getCell(4));
        String description = getStringCellValue(row.getCell(5));
        String monthOfProcess = getStringCellValue(row.getCell(6));
        String yearOfProcess = getStringCellValue(row.getCell(7));

        nip = nip != null ? nip.trim() : "";
        name = name != null ? name.trim() : "";
        grade = grade != null ? grade.trim() : "";
        paymentType = paymentType != null ? paymentType.trim() : "";
        description = description != null ? description.trim() : "";
        monthOfProcess = monthOfProcess != null ? monthOfProcess.trim() : "";
        yearOfProcess = yearOfProcess != null ? yearOfProcess.trim() : "";
        
        monthOfProcess = normalizeMonthValue(monthOfProcess);
        yearOfProcess = normalizeYearValue(yearOfProcess);
        
        PaymentDataDTO paymentData = PaymentDataDTO.builder()
            .nip(nip)
            .name(name)
            .grade(grade)
            .paymentType(paymentType)
            .amount(amount)
            .description(description)
            .monthOfProcess(monthOfProcess)
            .yearOfProcess(yearOfProcess)
            .build();
        
        performNipAndFieldValidation(paymentData);

        return paymentData;
    }

    private String verifyEmployeeExists(String nip) {
        try {
            log.info("Verifying employee with NIP: {}", nip);
            
            ResponseEntity<EpiccEmployeeProfileImpl> response = employeeClient.getProfile(nip);
            
            log.info("Received employee profile response for NIP {}: {}", nip, 
                    response != null ? "Found" : "Not Found");
            
            if (response != null && response.getBody() != null) {
                return NIP_VALID_MESSAGE;
            } else {
                return NIP_INVALID_MESSAGE;
            }
        } catch (Exception e) {
            log.error("Error checking employee data for NIP: {}", nip, e);
            return "NIP validation error: " + e.getMessage();
        }
    }

    private void validateRequiredFields(PaymentDataDTO data) {
        validateNip(data);
        validateNameGrade(data);
        validatePaymentType(data);
        validateAmount(data.getAmount());
        validateDescription(data.getDescription());
        validateMonthOfProcess(data.getMonthOfProcess());
        validateYearOfProcess(data.getYearOfProcess());
        validateDuplicate(data);
        validateEligibility(data);
    }

    private String validateGradeAndGetMessage(String grade) {
        try {
            validateNotEmpty(grade, "Grade");
            validateInSet(grade, VALID_GRADES, "grade",
                "Valid grades are: " + String.join(", ", VALID_GRADES));
            return GRADE_VALID_MESSAGE;
        } catch (CustomBadRequestException e) {
            return GRADE_INVALID_MESSAGE + ": " + e.getMessage();
        }
    }

    private String validatePaymentTypeAndGetMessage(String paymentType) {
        try {
            validateNotEmpty(paymentType, "Payment Type");
            validateInSet(paymentType, VALID_PAYMENT_TYPES, "payment type",
                "Valid payment types are: " + String.join(", ", VALID_PAYMENT_TYPES));
            return PAYMENT_TYPE_VALID_MESSAGE;
        } catch (CustomBadRequestException e) {
            return PAYMENT_TYPE_INVALID_MESSAGE + ": " + e.getMessage();
        }
    }

    private void validateNip(PaymentDataDTO data) {
        String nipValidationMessage = verifyEmployeeExists(data.getNip());
        boolean isNipValid = nipValidationMessage.equals(NIP_VALID_MESSAGE);

        if (isNipValid) {
            fetchEmployeeDetails(data.getNip(), data);
        } else {
            data.setTerminationDate("-");
            data.setSlik("-");
            data.setSanction("-");
            data.setDirectorate("-");
        }

        data.setNipValid(nipValidationMessage);
    }
    
    private void validateNameGrade(PaymentDataDTO data) {
        if (data.getGrade() != null && !data.getGrade().trim().isEmpty()) {
            String gradeValidationMessage = validateGradeAndGetMessage(data.getGrade());
            data.setGradeValid(gradeValidationMessage);
        } else {
            data.setGradeValid(GRADE_INVALID_MESSAGE + ": Grade not available from user input or employee profile");
        }
    }

    private void validateGrade(PaymentDataDTO data) {
        String gradeValidationMessage = validateGradeAndGetMessage(data.getGrade());
        data.setGradeValid(gradeValidationMessage);
    }

    private void validatePaymentType(PaymentDataDTO data) {
        String paymentTypeValidationMessage = validatePaymentTypeAndGetMessage(data.getPaymentType());
        data.setPaymentValid(paymentTypeValidationMessage);
    }

    private void validateDuplicate(PaymentDataDTO data) {
        boolean isDuplicate = checkForDuplicateSubmission(
                data.getNip(),
                data.getMonthOfProcess(),
                data.getYearOfProcess(),
                data.getPaymentType()
        );
        
        if (isDuplicate) {
            data.setIsDuplicate("Duplicate submission found for this NIP, payment type, month and year combination");
        } else {
            data.setIsDuplicate(null);
        }
    }

    private void validateAmount(BigDecimal amount) {
        if (amount == null) {
            throw new CustomBadRequestException(400, "Validation Error", "Amount is required");
        }
    }

    private void validateDescription(String description) {
        validateNotEmpty(description, "Description");
    }

    private void validateMonthOfProcess(String monthOfProcess) {
        validateNotEmpty(monthOfProcess, "Month of Process");
    }

    private void validateYearOfProcess(String yearOfProcess) {
        validateNotEmpty(yearOfProcess, "Year of Process");
        validateYearFormat(yearOfProcess);
    }

    private void validateNotEmpty(String value, String fieldName) {
        if (value == null || value.isEmpty()) {
            throw new CustomBadRequestException(400, "Validation Error", fieldName + " is required");
        }
    }

    private void validateInSet(String value, Set<String> validValues, String fieldType, String validValuesMessage) {
        if (!validValues.contains(value)) {
            throw new CustomBadRequestException(400, "Validation Error",
                "Invalid " + fieldType + ": " + value + ". " + validValuesMessage);
        }
    }

    private void validateYearFormat(String yearOfProcess) {
        try {
            int year = Integer.parseInt(yearOfProcess.trim());
            if (!isValidYear(year)) {
                throw new CustomBadRequestException(400, "Validation Error",
                    "Year of Process must be between 2020 and 2100, got: " + year);
            }
        } catch (NumberFormatException e) {
            throw new CustomBadRequestException(400, "Validation Error",
                "Year of Process must be a valid 4-digit year, got: " + yearOfProcess);
        }
    }

    private void validateEligibility(PaymentDataDTO data) {
        try {
            boolean isNipValid = data.getNipValid() != null && data.getNipValid().equals(NIP_VALID_MESSAGE);
            boolean isGradeValid = data.getGradeValid() != null && data.getGradeValid().equals(GRADE_VALID_MESSAGE);
            boolean isPaymentTypeValid = data.getPaymentValid() != null && data.getPaymentValid().equals(PAYMENT_TYPE_VALID_MESSAGE);
            boolean isDuplicate = data.getIsDuplicate() != null;

            if (!isNipValid || !isGradeValid || !isPaymentTypeValid || isDuplicate) {
                data.setEligible(false);
                return;
            }

            boolean isSlikEligible = isSlikEligible(data.getSlik());

            boolean isSanctionEligible = isSanctionEligible(data.getSanction());

            boolean isTerminationDateEligible = isTerminationDateEligible(
                    data.getTerminationDate(),
                    data.getMonthOfProcess(),
                    data.getYearOfProcess()
            );

            boolean isEligible = isSlikEligible && isSanctionEligible && isTerminationDateEligible;
            data.setEligible(isEligible);

            log.debug("[ValidationService] Eligibility validation for NIP {}: SLIK={}, Sanction={}, TerminationDate={}, Final={}",
                    data.getNip(), isSlikEligible, isSanctionEligible, isTerminationDateEligible, isEligible);

        } catch (Exception e) {
            log.error("[ValidationService] Error during eligibility validation for NIP: {}", data.getNip(), e);
            data.setEligible(false);
        }
    }

    private boolean isSlikEligible(String slik) {
        if (slik == null || slik.equals("-")) {
            return true;
        }
        return slik.equals("1") || slik.equals("2");
    }

    private boolean isSanctionEligible(String sanction) {
        if (sanction == null || sanction.equals("-")) {
            return true;
        }
        return sanction.equalsIgnoreCase("Not exist");
    }

    private boolean isTerminationDateEligible(String terminationDate, String monthOfProcess, String yearOfProcess) {
        if (terminationDate == null || terminationDate.equals("-")) {
            return true;
        }

        try {
            LocalDate termDate = parseTerminationDate(terminationDate);

            if (termDate == null) {
                return true;
            }
            LocalDate processDate = createProcessDate(monthOfProcess, yearOfProcess);
            if (processDate == null) {
                return true;
            }

            return termDate.isAfter(processDate);

        } catch (Exception e) {
            log.warn("[ValidationService] Error parsing dates for termination eligibility check. TerminationDate: {}, Month: {}, Year: {}, Error: {}",
                terminationDate, monthOfProcess, yearOfProcess, e.getMessage());
            return true;
        }
    }

    private LocalDate parseTerminationDate(String terminationDate) {
        if (terminationDate == null || terminationDate.trim().isEmpty() || terminationDate.equals("-")) {
            return null;
        }

        DateTimeFormatter[] formatters = {
                DateTimeFormatter.ofPattern("yyyy-MM-dd"),
                DateTimeFormatter.ofPattern("dd-MM-yyyy"),
                DateTimeFormatter.ofPattern("dd/MM/yyyy"),
                DateTimeFormatter.ofPattern("MM/dd/yyyy"),
                DateTimeFormatter.ofPattern("dd-MMM-yyyy"),
                DateTimeFormatter.ofPattern("yyyy/MM/dd")
        };

        String trimmedDate = terminationDate.trim();

        for (DateTimeFormatter formatter : formatters) {
            try {
                return LocalDate.parse(trimmedDate, formatter);
            } catch (DateTimeParseException e) {
                log.debug("[ValidationService] Failed to parse date '{}' with formatter '{}': {}",
                    trimmedDate, formatter, e.getMessage());
            }
        }

        log.warn("[ValidationService] Could not parse termination date: {}", terminationDate);
        return null;
    }

    private LocalDate createProcessDate(String monthOfProcess, String yearOfProcess) {
        try {
            int year = Integer.parseInt(yearOfProcess.trim());

            String normalizedMonth = normalizeMonthValue(monthOfProcess);
            int month = getMonthNumberFromNormalized(normalizedMonth);

            if (month == 0) {
                log.warn("[ValidationService] Invalid month for process date: {}", monthOfProcess);
                return null;
            }

            return LocalDate.of(year, month, 1);

        } catch (Exception e) {
            log.warn("[ValidationService] Error creating process date from month: {}, year: {}",
                    monthOfProcess, yearOfProcess, e);
            return null;
        }
    }

    private int getMonthNumberFromNormalized(String normalizedMonth) {
        if (normalizedMonth == null || normalizedMonth.trim().isEmpty()) {
            return 0;
        }

        return switch (normalizedMonth.trim()) {
            case "January" -> 1;
            case "February" -> 2;
            case "March" -> 3;
            case "April" -> 4;
            case "May" -> 5;
            case "June" -> 6;
            case "July" -> 7;
            case "August" -> 8;
            case "September" -> 9;
            case "October" -> 10;
            case "November" -> 11;
            case "December" -> 12;
            default -> 0;
        };
    }


    private void fetchEmployeeDetails(String nip, PaymentDataDTO paymentData) {
        try {
            ResponseEntity<EpiccEmployeeProfileImpl> response = employeeClient.getProfile(nip);
            if (response != null && response.getBody() != null) {
                EpiccEmployeeProfileImpl profile = response.getBody();
                
                String directorate = profile.getDirectorate();
                paymentData.setDirectorate(directorate != null && !directorate.isEmpty() ? directorate : "-");
                
                String terminationDate = profile.getTerminationDate();
                paymentData.setTerminationDate(terminationDate != null && !terminationDate.isEmpty() ? terminationDate : "-");
                
                String employeeGrade = profile.getGrade();
                String providedGrade = paymentData.getGrade();

                if (employeeGrade != null && !employeeGrade.isEmpty()) {
                    if (providedGrade != null && !providedGrade.isEmpty() && !providedGrade.equals(employeeGrade)) {
                        log.info("[ValidationService] Grade mismatch for NIP {}: provided '{}', using employee profile '{}'",
                            paymentData.getNip(), providedGrade, employeeGrade);
                    }
                    paymentData.setGrade(employeeGrade);
                } else if (providedGrade != null && !providedGrade.isEmpty()) {
                    log.info("[ValidationService] Using provided grade '{}' for NIP {} (no grade in employee profile)",
                        providedGrade, paymentData.getNip());
                }
                
                String fullName = buildFullName(profile.getFirstName(), profile.getMiddleName(), profile.getLastName());
                String providedName = paymentData.getName();

                if (fullName != null && !fullName.isEmpty()) {
                    if (providedName != null && !providedName.isEmpty() && !providedName.equals(fullName)) {
                        log.info("[ValidationService] Name mismatch for NIP {}: provided '{}', using employee profile '{}'",
                            paymentData.getNip(), providedName, fullName);
                    }
                    paymentData.setName(fullName);
                } else if (providedName != null && !providedName.isEmpty()) {
                    log.info("[ValidationService] Using provided name '{}' for NIP {} (no name in employee profile)",
                        providedName, paymentData.getNip());
                } else {
                    log.warn("[ValidationService] No name available from either source for NIP: {}", nip);
                }

                fetchSlikAndSanctionData(nip, paymentData);

            } else {
                paymentData.setTerminationDate("-");
                paymentData.setSlik("-");
                paymentData.setSanction("-");
                paymentData.setDirectorate("-");
            }
        } catch (Exception e) {
            log.error("Error fetching employee details for NIP: {}", nip, e);
            paymentData.setTerminationDate("-");
            paymentData.setSlik("-");
            paymentData.setSanction("-");
            paymentData.setDirectorate("-");
        }
    }

    private void fetchSlikAndSanctionData(String nip, PaymentDataDTO paymentData) {
        try {
            Map<String, SlikSanctionDataDTO> slikSanctionMap = fetchSlikAndSanctionDataBulk(Collections.singletonList(nip));
            SlikSanctionDataDTO data = slikSanctionMap.get(nip);

            if (data != null) {
                paymentData.setSlik(data.getSlik() != null ? String.valueOf(data.getSlik()) : "-");
                paymentData.setSanction(data.getSanction() != null ? data.getSanction() : "-");
            } else {
                paymentData.setSlik("-");
                paymentData.setSanction("-");
            }
        } catch (Exception e) {
            log.error("Error fetching SLIK and sanction data for NIP: {}", nip, e);
            paymentData.setSlik("-");
            paymentData.setSanction("-");
        }
    }

    private Map<String, SlikSanctionDataDTO> fetchSlikAndSanctionDataBulk(List<String> nips) {
        Map<String, SlikSanctionDataDTO> result = new HashMap<>();

        if (nips == null || nips.isEmpty()) {
            return result;
        }

        try {
            String nipParameter = String.join(",", nips);

            ExecutionRequest executionRequest = new ExecutionRequest();
            executionRequest.setLogicCode(staggeredPaymentValidationLogicCode);

            Map<String, Object> parameters = new HashMap<>();
            parameters.put("nip", nipParameter);
            executionRequest.setParameters(parameters);

            log.info("Executing SLIK and sanction validation for NIPs: {}", nipParameter);

            ApiResponse response = executionClient.executeLogic(executionRequest);

            if (response != null && response.isSuccess() && response.getData() != null) {

                Map<String, Object> responseData = (Map<String, Object>) response.getData();
                List<Map<String, Object>> resultList = (List<Map<String, Object>>) responseData.get("result");

                if (resultList != null) {
                    for (Map<String, Object> item : resultList) {
                        String nipFromResponse = (String) item.get("nip");
                        Object slikValue = item.get("slik");
                        String sanctionValue = (String) item.get("sanction");

                        SlikSanctionDataDTO data = new SlikSanctionDataDTO(slikValue, sanctionValue);

                        result.put(nipFromResponse, data);
                    }
                }
                log.info("Successfully fetched SLIK and sanction data for {} NIPs", result.size());
            } else {
                log.warn("Failed to fetch SLIK and sanction data: {}",
                    response != null ? response.getMessage() : "No response");
            }
        } catch (Exception e) {
            log.error("Error executing SLIK and sanction validation for NIPs: {}", nips, e);
        }
        return result;
    }

    private void bulkFetchSlikAndSanctionData(List<PaymentDataDTO> paymentDataList) {
        if (paymentDataList == null || paymentDataList.isEmpty()) {
            return;
        }

        List<String> validNips = paymentDataList.stream()
            .filter(data -> data.getNipValid() != null && data.getNipValid().equals(NIP_VALID_MESSAGE))
            .map(PaymentDataDTO::getNip)
            .distinct()
            .toList();

        if (validNips.isEmpty()) {
            log.info("No valid NIPs found for SLIK and sanction data fetching");
            return;
        }

        log.info("Bulk fetching SLIK and sanction data for {} unique NIPs", validNips.size());

        Map<String, SlikSanctionDataDTO> slikSanctionMap = fetchSlikAndSanctionDataBulk(validNips);

        for (PaymentDataDTO paymentData : paymentDataList) {
            if (paymentData.getNipValid() != null && paymentData.getNipValid().equals(NIP_VALID_MESSAGE)) {
                SlikSanctionDataDTO data = slikSanctionMap.get(paymentData.getNip());
                if (data != null) {
                    paymentData.setSlik(data.getSlik() != null ? String.valueOf(data.getSlik()) : "-");
                    paymentData.setSanction(data.getSanction() != null ? data.getSanction() : "-");
                } else {
                    paymentData.setSlik("-");
                    paymentData.setSanction("-");
                }
            }
        }

        log.info("Successfully applied SLIK and sanction data to payment records");
    }

    private String getStringCellValue(Cell cell) {
        if (cell == null) return null;

        return switch (cell.getCellType()) {
            case STRING -> cell.getStringCellValue();
            case NUMERIC -> String.valueOf((long) cell.getNumericCellValue());
            case BOOLEAN -> String.valueOf(cell.getBooleanCellValue());
            default -> null;
        };
    }
    

    private BigDecimal getBigDecimalCellValue(Cell cell) {
        if (cell == null) return null;
        
        switch (cell.getCellType()) {
            case NUMERIC:
                return BigDecimal.valueOf(cell.getNumericCellValue());
            case STRING:
                try {
                    return new BigDecimal(cell.getStringCellValue());
                } catch (NumberFormatException e) {
                    return null;
                }
            default:
                return null;
        }
    }

    private String normalizeMonthValue(String monthValue) {
        if (monthValue == null || monthValue.isEmpty()) {
            return monthValue;
        }
        
        try {
            int monthNum = Integer.parseInt(monthValue.trim());
            if (monthNum >= 1 && monthNum <= 12) {
                return switch (monthNum) {
                    case 1 -> "January";
                    case 2 -> "February";
                    case 3 -> "March";
                    case 4 -> "April";
                    case 5 -> "May";
                    case 6 -> "June";
                    case 7 -> "July";
                    case 8 -> "August";
                    case 9 -> "September";
                    case 10 -> "October";
                    case 11 -> "November";
                    case 12 -> "December";
                    default -> monthValue;
                };
            }
            return monthValue;
        } catch (NumberFormatException e) {
            String lowercaseMonth = monthValue.toLowerCase().trim();
            return switch (lowercaseMonth) {
                case "january", "jan" -> "January";
                case "february", "feb" -> "February";
                case "march", "mar" -> "March";
                case "april", "apr" -> "April";
                case "may" -> "May";
                case "june", "jun" -> "June";
                case "july", "jul" -> "July";
                case "august", "aug" -> "August";
                case "september", "sep", "sept" -> "September";
                case "october", "oct" -> "October";
                case "november", "nov" -> "November";
                case "december", "dec" -> "December";

                case "januari" -> "January";
                case "februari" -> "February";
                case "maret" -> "March";
                case "mei" -> "May";
                case "juni" -> "June";
                case "juli" -> "July";
                case "agustus" -> "August";
                case "oktober" -> "October";
                case "desember" -> "December";

                default -> monthValue;
            };
        }
    }
    private String normalizeYearValue(String yearValue) {
        if (yearValue == null || yearValue.trim().isEmpty()) {
            return yearValue;
        }

        String trimmedYear = yearValue.trim();

        try {
            int year = Integer.parseInt(trimmedYear);
            if (isValidYear(year)) {
                return String.valueOf(year);
            }
            return yearValue;
        } catch (NumberFormatException e) {
            log.debug("[ValidationService] Year value '{}' is not a simple integer, trying regex extraction", trimmedYear);
        }

        String yearPattern = "\\b(20\\d{2})\\b";
        java.util.regex.Pattern pattern = java.util.regex.Pattern.compile(yearPattern);
        java.util.regex.Matcher matcher = pattern.matcher(trimmedYear);

        if (matcher.find()) {
            String extractedYear = matcher.group();
            int year = Integer.parseInt(extractedYear);
            if (isValidYear(year)) {
                log.info("[ValidationService] Extracted year {} from text: {}", year, trimmedYear);
                return String.valueOf(year);
            }
        }

        log.warn("[ValidationService] Could not extract valid year from: {}", yearValue);
        return yearValue;
    }

    private boolean isValidYear(int year) {
        return year >= 2020 && year <= 2100;
    }

    public GeneralBodyResponse validateSinglePayment(SingleValidRequestDTO request) {
        log.info("[ValidationService] Starting to process single payment validation for NIP: {}", request.getNip());
        
        PaymentDataDTO paymentData = PaymentDataDTO.builder()
            .nip(request.getNip())
            .name(request.getName())
            .grade(request.getGrade())
            .paymentType(request.getPaymentType())
            .amount(request.getAmount())
            .description(request.getDescription())
            .monthOfProcess(normalizeMonthValue(request.getMonthOfProcess()))
            .yearOfProcess(normalizeYearValue(request.getYearOfProcess()))
            .build();
        
        performSingleValidation(paymentData);
        
        log.info("[ValidationService] Single payment validation completed for NIP: {}", request.getNip());
        
        return GeneralBodyResponse.builder()
            .code(200)
            .status("OK")
            .message("Payment data validated successfully")
            .data(paymentData)
            .build();
    }

    private String buildFullName(String firstName, String middleName, String lastName) {
        StringBuilder fullName = new StringBuilder();
        
        if (firstName != null && !firstName.trim().isEmpty()) {
            fullName.append(firstName.trim());
        }
        
        if (middleName != null && !middleName.trim().isEmpty()) {
            if (!fullName.isEmpty()) {
                fullName.append(" ");
            }
            fullName.append(middleName.trim());
        }
        
        if (lastName != null && !lastName.trim().isEmpty()) {
            if (!fullName.isEmpty()) {
                fullName.append(" ");
            }
            fullName.append(lastName.trim());
        }
        
        return !fullName.isEmpty() ? fullName.toString() : null;
    }

    private boolean checkForDuplicateSubmission(String nip, String monthOfProcess, String yearOfProcess, String paymentType) {
        log.info("[ValidationService] Checking for duplicate submission - NIP: {}, Month: {}, Year: {}, PaymentType: {}",
                nip, monthOfProcess, yearOfProcess, paymentType);

        boolean exists = submissionRepository.existsByCompositeKey(
                nip, monthOfProcess, yearOfProcess, paymentType);

        if (exists) {
            log.warn("[ValidationService] Duplicate submission found for NIP: {}, Month: {}, Year: {}, PaymentType: {}",
                    nip, monthOfProcess, yearOfProcess, paymentType);
        }

        return exists;
    }
    
    private void performSingleValidation(PaymentDataDTO paymentData) {
        validateRequiredFields(paymentData);
    }

    private void performNipAndFieldValidation(PaymentDataDTO paymentData) {
        validateNip(paymentData);
        validateGrade(paymentData);
        validatePaymentType(paymentData);
        paymentData.setIsDuplicate(null);

        validateEligibility(paymentData);
    }

    private void checkAndMarkDuplicates(List<PaymentDataDTO> paymentDataList) {
        log.info("[ValidationService] Checking for duplicates within batch and against database for {} records", paymentDataList.size());

        Set<String> batchCompositeKeys = new HashSet<>();

        for (PaymentDataDTO paymentData : paymentDataList) {
            String compositeKey = createCompositeKey(paymentData.getNip(), paymentData.getMonthOfProcess(),
                                                   paymentData.getYearOfProcess(), paymentData.getPaymentType());

            boolean isDuplicateInBatch = !batchCompositeKeys.add(compositeKey);
            boolean isDuplicateInDatabase = false;

            if (!isDuplicateInBatch) {
                isDuplicateInDatabase = checkForDuplicateSubmission(
                    paymentData.getNip(),
                    paymentData.getMonthOfProcess(),
                    paymentData.getYearOfProcess(),
                    paymentData.getPaymentType()
                );
            }

            boolean isDuplicate = isDuplicateInBatch || isDuplicateInDatabase;

            if (isDuplicate) {
                paymentData.setEligible(false);
                String duplicateSource = isDuplicateInBatch ? "within current file" : "in database";
                paymentData.setIsDuplicate("Duplicate submission found " + duplicateSource + " for this NIP, payment type, month and year combination");
                log.warn("[ValidationService] Duplicate found {} for composite key: {}", duplicateSource, compositeKey);
            } else {
                paymentData.setIsDuplicate(null);
            }
        }
    }

    private String createCompositeKey(String nip, String monthOfProcess, String yearOfProcess, String paymentType) {
        return nip + "|" + monthOfProcess + "|" + yearOfProcess + "|" + paymentType;
    }

    private boolean isEmptyRow(Row row) {
        if (row == null) {
            return true;
        }
        String nip = getStringCellValue(row.getCell(0));
        String grade = getStringCellValue(row.getCell(2));
        String paymentType = getStringCellValue(row.getCell(3));
        BigDecimal amount = getBigDecimalCellValue(row.getCell(4));

        return (nip == null || nip.trim().isEmpty()) &&
               (grade == null || grade.trim().isEmpty()) &&
               (paymentType == null || paymentType.trim().isEmpty()) &&
               amount == null;
    }

}
