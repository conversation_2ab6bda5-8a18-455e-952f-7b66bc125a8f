package com.dwdo.hotdesk.service;

import com.dwdo.hotdesk.dto.PaymentDataDTO;
import com.dwdo.hotdesk.dto.response.GeneralBodyResponse;
import com.dwdo.hotdesk.repository.PaymentSubmissionRepository;
import com.dwdo.hotdesk.restcontrolleradvice.CustomBadRequestException;
import com.dwdo.hotdesk.service.feign.EmployeeClient;
import com.dwdo.hotdesk.service.feign.response.EpiccEmployeeProfileImpl;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.http.ResponseEntity;
import org.springframework.mock.web.MockMultipartFile;
import org.springframework.test.util.ReflectionTestUtils;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.math.BigDecimal;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;

/**
 * Comprehensive unit tests for PaymentValidationService Excel file processing
 * Tests Excel file validation, parsing, and data extraction functionality
 */
@ExtendWith(MockitoExtension.class)
@DisplayName("Payment Validation Service Excel Tests")
class PaymentValidationServiceExcelTest {

    @Mock
    private EmployeeClient employeeClient;

    @Mock
    private PaymentSubmissionRepository submissionRepository;

    @InjectMocks
    private PaymentValidationService paymentValidationService;

    private EpiccEmployeeProfileImpl sampleEmployeeProfile;

    @BeforeEach
    void setUp() {
        // Inject the mocks using reflection since they're @Autowired
        ReflectionTestUtils.setField(paymentValidationService, "employeeClient", employeeClient);
        ReflectionTestUtils.setField(paymentValidationService, "submissionRepository", submissionRepository);

        // Setup sample employee profile
        sampleEmployeeProfile = new EpiccEmployeeProfileImpl();
        sampleEmployeeProfile.setDirectorate("IT");
        sampleEmployeeProfile.setTerminationDate(null);
    }

    @Test
    @DisplayName("Should successfully process valid Excel file with single row")
    void testValidateExcelFile_ValidSingleRow() throws IOException {
        // Given
        MockMultipartFile validExcelFile = createValidExcelFile();
        
        when(employeeClient.getProfile("123456789"))
                .thenReturn(ResponseEntity.ok(sampleEmployeeProfile));

        // When
        GeneralBodyResponse response = paymentValidationService.validateExcelFile(validExcelFile);

        // Then
        assertNotNull(response);
        assertEquals(200, response.getCode());
        assertEquals("OK", response.getStatus());
        assertEquals("File validated successfully", response.getMessage());

        @SuppressWarnings("unchecked")
        List<PaymentDataDTO> paymentDataList = (List<PaymentDataDTO>) response.getData();
        assertNotNull(paymentDataList);
        assertEquals(1, paymentDataList.size());

        PaymentDataDTO paymentData = paymentDataList.get(0);
        assertEquals("123456789", paymentData.getNip());
        assertEquals("John Doe", paymentData.getName());
        assertEquals("U5", paymentData.getGrade());
        assertEquals("Bonus Staggered", paymentData.getPaymentType());
        assertEquals(new BigDecimal("5000000.0"), paymentData.getAmount());
        assertEquals("Test bonus", paymentData.getDescription());
        assertEquals("January", paymentData.getMonthOfProcess());
        assertEquals("2024", paymentData.getYearOfProcess());
        assertEquals("NIP is valid", paymentData.getNipValid());
        assertEquals("IT", paymentData.getDirectorate());
        assertTrue(paymentData.getEligible());
    }

    @Test
    @DisplayName("Should successfully process valid Excel file with multiple rows")
    void testValidateExcelFile_ValidMultipleRows() throws IOException {
        // Given
        MockMultipartFile validExcelFile = createValidExcelFileWithMultipleRows();
        
        when(employeeClient.getProfile(anyString()))
                .thenReturn(ResponseEntity.ok(sampleEmployeeProfile));

        // When
        GeneralBodyResponse response = paymentValidationService.validateExcelFile(validExcelFile);

        // Then
        assertNotNull(response);
        assertEquals(200, response.getCode());

        @SuppressWarnings("unchecked")
        List<PaymentDataDTO> paymentDataList = (List<PaymentDataDTO>) response.getData();
        assertNotNull(paymentDataList);
        assertEquals(2, paymentDataList.size());

        // Verify first row
        PaymentDataDTO firstPayment = paymentDataList.get(0);
        assertEquals("123456789", firstPayment.getNip());
        assertEquals("John Doe", firstPayment.getName());

        // Verify second row
        PaymentDataDTO secondPayment = paymentDataList.get(1);
        assertEquals("987654321", secondPayment.getNip());
        assertEquals("Jane Smith", secondPayment.getName());
    }

    @Test
    @DisplayName("Should handle Excel file with empty rows")
    void testValidateExcelFile_EmptyRows() throws IOException {
        // Given
        MockMultipartFile excelFileWithEmptyRows = createExcelFileWithEmptyRows();

        // When
        GeneralBodyResponse response = paymentValidationService.validateExcelFile(excelFileWithEmptyRows);

        // Then
        assertNotNull(response);
        assertEquals(200, response.getCode());

        @SuppressWarnings("unchecked")
        List<PaymentDataDTO> paymentDataList = (List<PaymentDataDTO>) response.getData();
        assertNotNull(paymentDataList);
        assertTrue(paymentDataList.isEmpty()); // Should skip empty rows
    }

    @Test
    @DisplayName("Should handle Excel file with invalid data and skip invalid rows")
    void testValidateExcelFile_InvalidDataRows() throws IOException {
        // Given
        MockMultipartFile excelFileWithInvalidData = createExcelFileWithInvalidData();
        
        when(employeeClient.getProfile("123456789"))
                .thenReturn(ResponseEntity.ok(sampleEmployeeProfile));

        // When
        GeneralBodyResponse response = paymentValidationService.validateExcelFile(excelFileWithInvalidData);

        // Then
        assertNotNull(response);
        assertEquals(200, response.getCode());

        @SuppressWarnings("unchecked")
        List<PaymentDataDTO> paymentDataList = (List<PaymentDataDTO>) response.getData();
        assertNotNull(paymentDataList);
        assertEquals(1, paymentDataList.size()); // Only valid row should be processed

        PaymentDataDTO validPayment = paymentDataList.get(0);
        assertEquals("123456789", validPayment.getNip());
    }

    @Test
    @DisplayName("Should throw exception when all rows are invalid")
    void testValidateExcelFile_AllRowsInvalid() throws IOException {
        // Given
        MockMultipartFile excelFileWithAllInvalidData = createExcelFileWithAllInvalidData();

        // When & Then
        CustomBadRequestException exception = assertThrows(CustomBadRequestException.class, () -> {
            paymentValidationService.validateExcelFile(excelFileWithAllInvalidData);
        });

        assertEquals(400, exception.getCode());
        assertEquals("Validation Error", exception.getStatus());
        assertTrue(exception.getMessage().contains("No valid payment records found"));
    }

    @Test
    @DisplayName("Should handle Excel file with only header row")
    void testValidateExcelFile_OnlyHeaderRow() throws IOException {
        // Given
        MockMultipartFile excelFileWithOnlyHeader = createExcelFileWithOnlyHeader();

        // When
        GeneralBodyResponse response = paymentValidationService.validateExcelFile(excelFileWithOnlyHeader);

        // Then
        assertNotNull(response);
        assertEquals(200, response.getCode());

        @SuppressWarnings("unchecked")
        List<PaymentDataDTO> paymentDataList = (List<PaymentDataDTO>) response.getData();
        assertNotNull(paymentDataList);
        assertTrue(paymentDataList.isEmpty());
    }

    @Test
    @DisplayName("Should handle Excel file with duplicate entries")
    void testValidateExcelFile_DuplicateEntries() throws IOException {
        // Given
        MockMultipartFile excelFileWithDuplicates = createExcelFileWithDuplicates();
        
        when(employeeClient.getProfile("123456789"))
                .thenReturn(ResponseEntity.ok(sampleEmployeeProfile));

        // When
        GeneralBodyResponse response = paymentValidationService.validateExcelFile(excelFileWithDuplicates);

        // Then
        assertNotNull(response);
        assertEquals(200, response.getCode());

        @SuppressWarnings("unchecked")
        List<PaymentDataDTO> paymentDataList = (List<PaymentDataDTO>) response.getData();
        assertNotNull(paymentDataList);
        assertEquals(2, paymentDataList.size());

        // First entry should be eligible
        PaymentDataDTO firstEntry = paymentDataList.get(0);
        assertTrue(firstEntry.getEligible());

        // Second entry (duplicate) should not be eligible
        PaymentDataDTO secondEntry = paymentDataList.get(1);
        assertFalse(secondEntry.getEligible());
        assertTrue(secondEntry.getNipValid().contains("Duplicate submission found within current file"));
    }



    /**
     * Helper method to create a valid Excel file with single row
     */
    private MockMultipartFile createValidExcelFile() throws IOException {
        try (Workbook workbook = new XSSFWorkbook()) {
            Sheet sheet = workbook.createSheet("Payment Data");
            
            // Create header row
            Row headerRow = sheet.createRow(0);
            headerRow.createCell(0).setCellValue("NIP");
            headerRow.createCell(1).setCellValue("Name");
            headerRow.createCell(2).setCellValue("Grade");
            headerRow.createCell(3).setCellValue("Payment Type");
            headerRow.createCell(4).setCellValue("Amount");
            headerRow.createCell(5).setCellValue("Description");
            headerRow.createCell(6).setCellValue("Month");
            headerRow.createCell(7).setCellValue("Year");
            
            // Create data row
            Row dataRow = sheet.createRow(1);
            dataRow.createCell(0).setCellValue("123456789");
            dataRow.createCell(1).setCellValue("John Doe");
            dataRow.createCell(2).setCellValue("U5");
            dataRow.createCell(3).setCellValue("Bonus Staggered");
            dataRow.createCell(4).setCellValue(5000000);
            dataRow.createCell(5).setCellValue("Test bonus");
            dataRow.createCell(6).setCellValue("January");
            dataRow.createCell(7).setCellValue("2024");
            
            ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
            workbook.write(outputStream);
            
            return new MockMultipartFile(
                "file",
                "test.xlsx",
                "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
                outputStream.toByteArray()
            );
        }
    }

    /**
     * Helper method to create a valid Excel file with multiple rows
     */
    private MockMultipartFile createValidExcelFileWithMultipleRows() throws IOException {
        try (Workbook workbook = new XSSFWorkbook()) {
            Sheet sheet = workbook.createSheet("Payment Data");
            
            // Create header row
            Row headerRow = sheet.createRow(0);
            headerRow.createCell(0).setCellValue("NIP");
            headerRow.createCell(1).setCellValue("Name");
            headerRow.createCell(2).setCellValue("Grade");
            headerRow.createCell(3).setCellValue("Payment Type");
            headerRow.createCell(4).setCellValue("Amount");
            headerRow.createCell(5).setCellValue("Description");
            headerRow.createCell(6).setCellValue("Month");
            headerRow.createCell(7).setCellValue("Year");
            
            // Create first data row
            Row dataRow1 = sheet.createRow(1);
            dataRow1.createCell(0).setCellValue("123456789");
            dataRow1.createCell(1).setCellValue("John Doe");
            dataRow1.createCell(2).setCellValue("U5");
            dataRow1.createCell(3).setCellValue("Bonus Staggered");
            dataRow1.createCell(4).setCellValue(5000000);
            dataRow1.createCell(5).setCellValue("Test bonus");
            dataRow1.createCell(6).setCellValue("January");
            dataRow1.createCell(7).setCellValue("2024");
            
            // Create second data row
            Row dataRow2 = sheet.createRow(2);
            dataRow2.createCell(0).setCellValue("987654321");
            dataRow2.createCell(1).setCellValue("Jane Smith");
            dataRow2.createCell(2).setCellValue("U7");
            dataRow2.createCell(3).setCellValue("Performance Staggered");
            dataRow2.createCell(4).setCellValue(3000000);
            dataRow2.createCell(5).setCellValue("Performance bonus");
            dataRow2.createCell(6).setCellValue("February");
            dataRow2.createCell(7).setCellValue("2024");
            
            ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
            workbook.write(outputStream);
            
            return new MockMultipartFile(
                "file",
                "test.xlsx",
                "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
                outputStream.toByteArray()
            );
        }
    }

    /**
     * Helper method to create Excel file with empty rows
     */
    private MockMultipartFile createExcelFileWithEmptyRows() throws IOException {
        try (Workbook workbook = new XSSFWorkbook()) {
            Sheet sheet = workbook.createSheet("Payment Data");

            // Create header row
            Row headerRow = sheet.createRow(0);
            headerRow.createCell(0).setCellValue("NIP");
            headerRow.createCell(1).setCellValue("Name");
            headerRow.createCell(2).setCellValue("Grade");
            headerRow.createCell(3).setCellValue("Payment Type");
            headerRow.createCell(4).setCellValue("Amount");
            headerRow.createCell(5).setCellValue("Description");
            headerRow.createCell(6).setCellValue("Month");
            headerRow.createCell(7).setCellValue("Year");

            // Create empty rows (no data)
            sheet.createRow(1);
            sheet.createRow(2);

            ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
            workbook.write(outputStream);

            return new MockMultipartFile(
                "file",
                "test.xlsx",
                "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
                outputStream.toByteArray()
            );
        }
    }

    /**
     * Helper method to create Excel file with invalid data
     */
    private MockMultipartFile createExcelFileWithInvalidData() throws IOException {
        try (Workbook workbook = new XSSFWorkbook()) {
            Sheet sheet = workbook.createSheet("Payment Data");

            // Create header row
            Row headerRow = sheet.createRow(0);
            headerRow.createCell(0).setCellValue("NIP");
            headerRow.createCell(1).setCellValue("Name");
            headerRow.createCell(2).setCellValue("Grade");
            headerRow.createCell(3).setCellValue("Payment Type");
            headerRow.createCell(4).setCellValue("Amount");
            headerRow.createCell(5).setCellValue("Description");
            headerRow.createCell(6).setCellValue("Month");
            headerRow.createCell(7).setCellValue("Year");

            // Create invalid data row (missing required fields)
            Row invalidRow = sheet.createRow(1);
            invalidRow.createCell(0).setCellValue(""); // Empty NIP
            invalidRow.createCell(1).setCellValue("Invalid User");
            invalidRow.createCell(2).setCellValue("X1"); // Invalid grade
            invalidRow.createCell(3).setCellValue("Invalid Payment");
            invalidRow.createCell(4).setCellValue(1000000);
            invalidRow.createCell(5).setCellValue("Test");
            invalidRow.createCell(6).setCellValue("January");
            invalidRow.createCell(7).setCellValue("2024");

            // Create valid data row
            Row validRow = sheet.createRow(2);
            validRow.createCell(0).setCellValue("123456789");
            validRow.createCell(1).setCellValue("John Doe");
            validRow.createCell(2).setCellValue("U5");
            validRow.createCell(3).setCellValue("Bonus Staggered");
            validRow.createCell(4).setCellValue(5000000);
            validRow.createCell(5).setCellValue("Test bonus");
            validRow.createCell(6).setCellValue("January");
            validRow.createCell(7).setCellValue("2024");

            ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
            workbook.write(outputStream);

            return new MockMultipartFile(
                "file",
                "test.xlsx",
                "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
                outputStream.toByteArray()
            );
        }
    }

    /**
     * Helper method to create Excel file with all invalid data
     */
    private MockMultipartFile createExcelFileWithAllInvalidData() throws IOException {
        try (Workbook workbook = new XSSFWorkbook()) {
            Sheet sheet = workbook.createSheet("Payment Data");

            // Create header row
            Row headerRow = sheet.createRow(0);
            headerRow.createCell(0).setCellValue("NIP");
            headerRow.createCell(1).setCellValue("Name");
            headerRow.createCell(2).setCellValue("Grade");
            headerRow.createCell(3).setCellValue("Payment Type");
            headerRow.createCell(4).setCellValue("Amount");
            headerRow.createCell(5).setCellValue("Description");
            headerRow.createCell(6).setCellValue("Month");
            headerRow.createCell(7).setCellValue("Year");

            // Create invalid data row 1
            Row invalidRow1 = sheet.createRow(1);
            invalidRow1.createCell(0).setCellValue(""); // Empty NIP
            invalidRow1.createCell(1).setCellValue("Invalid User 1");
            invalidRow1.createCell(2).setCellValue("X1");
            invalidRow1.createCell(3).setCellValue("Invalid Payment");
            invalidRow1.createCell(4).setCellValue(1000000);
            invalidRow1.createCell(5).setCellValue("Test");
            invalidRow1.createCell(6).setCellValue("January");
            invalidRow1.createCell(7).setCellValue("2024");

            // Create invalid data row 2
            Row invalidRow2 = sheet.createRow(2);
            invalidRow2.createCell(0).setCellValue("123");
            invalidRow2.createCell(1).setCellValue(""); // Empty name
            invalidRow2.createCell(2).setCellValue("U5");
            invalidRow2.createCell(3).setCellValue("Bonus Staggered");
            invalidRow2.createCell(4).setCellValue(1000000);
            invalidRow2.createCell(5).setCellValue("Test");
            invalidRow2.createCell(6).setCellValue("January");
            invalidRow2.createCell(7).setCellValue("2024");

            ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
            workbook.write(outputStream);

            return new MockMultipartFile(
                "file",
                "test.xlsx",
                "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
                outputStream.toByteArray()
            );
        }
    }

    /**
     * Helper method to create Excel file with only header row
     */
    private MockMultipartFile createExcelFileWithOnlyHeader() throws IOException {
        try (Workbook workbook = new XSSFWorkbook()) {
            Sheet sheet = workbook.createSheet("Payment Data");

            // Create only header row
            Row headerRow = sheet.createRow(0);
            headerRow.createCell(0).setCellValue("NIP");
            headerRow.createCell(1).setCellValue("Name");
            headerRow.createCell(2).setCellValue("Grade");
            headerRow.createCell(3).setCellValue("Payment Type");
            headerRow.createCell(4).setCellValue("Amount");
            headerRow.createCell(5).setCellValue("Description");
            headerRow.createCell(6).setCellValue("Month");
            headerRow.createCell(7).setCellValue("Year");

            ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
            workbook.write(outputStream);

            return new MockMultipartFile(
                "file",
                "test.xlsx",
                "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
                outputStream.toByteArray()
            );
        }
    }

    /**
     * Helper method to create Excel file with duplicate entries
     */
    private MockMultipartFile createExcelFileWithDuplicates() throws IOException {
        try (Workbook workbook = new XSSFWorkbook()) {
            Sheet sheet = workbook.createSheet("Payment Data");

            // Create header row
            Row headerRow = sheet.createRow(0);
            headerRow.createCell(0).setCellValue("NIP");
            headerRow.createCell(1).setCellValue("Name");
            headerRow.createCell(2).setCellValue("Grade");
            headerRow.createCell(3).setCellValue("Payment Type");
            headerRow.createCell(4).setCellValue("Amount");
            headerRow.createCell(5).setCellValue("Description");
            headerRow.createCell(6).setCellValue("Month");
            headerRow.createCell(7).setCellValue("Year");

            // Create first data row
            Row dataRow1 = sheet.createRow(1);
            dataRow1.createCell(0).setCellValue("123456789");
            dataRow1.createCell(1).setCellValue("John Doe");
            dataRow1.createCell(2).setCellValue("U5");
            dataRow1.createCell(3).setCellValue("Bonus Staggered");
            dataRow1.createCell(4).setCellValue(5000000);
            dataRow1.createCell(5).setCellValue("Test bonus");
            dataRow1.createCell(6).setCellValue("January");
            dataRow1.createCell(7).setCellValue("2024");

            // Create duplicate data row (same NIP, payment type, month, year)
            Row dataRow2 = sheet.createRow(2);
            dataRow2.createCell(0).setCellValue("123456789");
            dataRow2.createCell(1).setCellValue("John Doe");
            dataRow2.createCell(2).setCellValue("U5");
            dataRow2.createCell(3).setCellValue("Bonus Staggered");
            dataRow2.createCell(4).setCellValue(3000000);
            dataRow2.createCell(5).setCellValue("Duplicate bonus");
            dataRow2.createCell(6).setCellValue("January");
            dataRow2.createCell(7).setCellValue("2024");

            ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
            workbook.write(outputStream);

            return new MockMultipartFile(
                "file",
                "test.xlsx",
                "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
                outputStream.toByteArray()
            );
        }
    }

}
